'use client';

import React, { useCallback } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  EmbeddedCheckoutProvider,
  EmbeddedCheckout
} from '@stripe/react-stripe-js';

// Make sure to call `loadStripe` outside of a component's render to avoid
// recreating the `Stripe` object on every render.
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface EmbeddedCheckoutComponentProps {
  clientSecret: string;
  onComplete?: () => void;
}

const EmbeddedCheckoutComponent: React.FC<EmbeddedCheckoutComponentProps> = ({
  clientSecret,
  onComplete
}) => {
  const fetchClientSecret = useCallback(() => {
    // Return the client secret directly since we already have it
    return Promise.resolve(clientSecret);
  }, [clientSecret]);

  const options = {
    fetchClientSecret,
    onComplete: onComplete || (() => {
      // Default completion handler - could redirect or show success message
      console.log('Payment completed successfully');
    })
  };

  return (
    <div id="checkout">
      <EmbeddedCheckoutProvider
        stripe={stripePromise}
        options={options}
      >
        <EmbeddedCheckout />
      </EmbeddedCheckoutProvider>
    </div>
  );
};

export default EmbeddedCheckoutComponent;
