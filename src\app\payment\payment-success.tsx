import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

export default function PaymentSuccess() {
  const searchParams = useSearchParams();
  const userId = searchParams.get('userId');

  useEffect(() => {
    if (userId) {
      fetch('/api/confirm', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId }),
      });
    }
  }, [userId]);

  return (
    <div>
      <h1>✅ Payment Successful</h1>
      <p>Your payment was successful and stored in Firebase (hardcoded).</p>
    </div>
  );
}
