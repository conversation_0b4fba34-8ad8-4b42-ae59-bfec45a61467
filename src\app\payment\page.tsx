"use client";

import { useState, useEffect } from 'react';
import { initFirebase } from '../../../firebaseConfig';
import { getAuth } from 'firebase/auth';
import { getAuthenticatedUserId, getAuthHeaders, getAuthenticatedUser, isAuthenticated } from '@/lib/auth/clientAuth';

interface ApiResponse {
  success: boolean;
  error?: string;
  [key: string]: any;
}

interface ApiField {
  name: string;
  type: 'text' | 'email' | 'number' | 'select' | 'textarea';
  required: boolean;
  placeholder?: string;
  options?: string[];
}

interface ApiEndpoint {
  name: string;
  endpoint: string;
  method: string;
  description: string;
  fields: ApiField[];
}

export default function StripeApiTester() {
  const [loading, setLoading] = useState<string | null>(null);
  const [results, setResults] = useState<Record<string, ApiResponse>>({});
  const [activeCategory, setActiveCategory] = useState<string>('checkout');
  const [formData, setFormData] = useState<Record<string, Record<string, string>>>({});
  const [onboardingStatus, setOnboardingStatus] = useState<any>(null);

  // Check for onboarding completion when page loads
  useEffect(() => {
    const checkOnboardingCompletion = async () => {
      try {
        // Check URL parameters for account and userId (returned from Stripe onboarding)
        const urlParams = new URLSearchParams(window.location.search);
        const accountId = urlParams.get('account');
        const userId = urlParams.get('userId');
        const onboardingComplete = urlParams.get('onboarding') === 'complete';

        if (accountId && userId && onboardingComplete) {
          console.log('Detected successful return from Stripe onboarding:', { accountId, userId });

          // Get authenticated user ID to verify it matches
          const authenticatedUserId = getAuthenticatedUserId();
          if (!authenticatedUserId) {
            console.warn('No authenticated user found');
            return;
          }

          if (authenticatedUserId !== userId) {
            console.warn('URL userId does not match authenticated user');
            return;
          }

          // Call the onboarding completion API
          const response = await fetch('/api/connect/onboarding-complete', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({ accountId })
          });

          const data = await response.json();

          if (data.success) {
            console.log('✅ Onboarding status updated successfully:', data);
            setOnboardingStatus(data.onboardingStatus);

            // Show success message
            setResults(prev => ({
              ...prev,
              'onboarding-complete': {
                success: true,
                message: `✅ Stripe onboarding ${data.onboardingStatus.complete ? 'completed successfully' : 'partially completed'}!
                  Account ${accountId} has charges ${data.onboardingStatus.chargesEnabled ? 'enabled' : 'disabled'} and payouts ${data.onboardingStatus.payoutsEnabled ? 'enabled' : 'disabled'}.`,
                onboardingStatus: data.onboardingStatus,
                account: data.account
              }
            }));

            // Clean up URL parameters
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
          } else {
            console.error('Failed to update onboarding status:', data.error);
          }
        }
      } catch (error) {
        console.error('Error checking onboarding completion:', error);
      }
    };

    checkOnboardingCompletion();
  }, []);

  const handleInputChange = (endpoint: string, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [endpoint]: {
        ...prev[endpoint],
        [field]: value
      }
    }));
  };

  const testApi = async (apiEndpoint: ApiEndpoint) => {
    setLoading(apiEndpoint.endpoint);
    try {
      // Check if user is authenticated for protected endpoints
      const userId = getAuthenticatedUserId();
      const isProtectedEndpoint = apiEndpoint.endpoint.includes('/connect/');

      if (isProtectedEndpoint && !userId) {
        setResults(prev => ({
          ...prev,
          [apiEndpoint.endpoint]: {
            error: 'Authentication required. Please log in to access this feature.',
            code: 'AUTH_REQUIRED',
            success: false
          }
        }));
        setLoading(null);
        return;
      }

      if (userId) {
        console.log('Using authenticated user ID:', userId);
      }

      const data = formData[apiEndpoint.endpoint] || {};

      // Convert string values to appropriate types
      const processedData: any = {};
      apiEndpoint.fields.forEach(field => {
        const value = data[field.name];
        if (value !== undefined && value !== '') {
          if (field.type === 'number') {
            processedData[field.name] = parseInt(value);
          } else if (field.name === 'paymentMethodTypes') {
            processedData[field.name] = value.split(',').map(s => s.trim());
          } else {
            processedData[field.name] = value;
          }
        }
      });

      let url = apiEndpoint.endpoint;

      let fetchOptions: RequestInit = {
        method: apiEndpoint.method,
        headers: getAuthHeaders(),
      };

      // Handle path parameters (e.g., {transactionId}, {sessionId})
      Object.entries(processedData).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          url = url.replace(`{${key}}`, String(value));
        }
      });

      if (apiEndpoint.method === 'GET') {
        // For GET requests, add remaining data as query parameters
        const queryParams = new URLSearchParams();
        Object.entries(processedData).forEach(([key, value]) => {
          // Skip if this was used as a path parameter
          if (!apiEndpoint.endpoint.includes(`{${key}}`) && value !== undefined && value !== '') {
            queryParams.append(key, String(value));
          }
        });
        if (queryParams.toString()) {
          url += '?' + queryParams.toString();
        }
      } else {
        // For POST/PUT/etc, send data in body (excluding path parameters)
        const bodyData: any = {};
        Object.entries(processedData).forEach(([key, value]) => {
          if (!apiEndpoint.endpoint.includes(`{${key}}`)) {
            bodyData[key] = value;
          }
        });
        fetchOptions.body = JSON.stringify(bodyData);
      }

      const response = await fetch(url, fetchOptions);

      const responseData = await response.json();

      // Special handling for Connect Login - open in popup or modal
      if (apiEndpoint.endpoint === '/api/connect/login-oauth' && responseData.success && responseData.loginUrl) {
        const useModal = processedData.useModal === 'true';

        if (useModal) {
          // Note: Stripe blocks iframe embedding for security, so we'll open in a centered popup instead
          const popup = window.open(
            responseData.loginUrl,
            'stripe-connect-login',
            'width=500,height=600,scrollbars=yes,resizable=yes,status=yes,location=yes,left=' +
            (window.screen.width / 2 - 250) + ',top=' + (window.screen.height / 2 - 300)
          );

          if (popup) {
            // Monitor centered popup for completion and auto-close on success
            const checkClosed = setInterval(async () => {
              if (popup.closed) {
                clearInterval(checkClosed);
                console.log('Stripe Connect centered popup closed - checking login status...');

                // Auto-check if login was successful by calling my-account endpoint
                try {
                  const userId = getAuthenticatedUserId();
                  if (!userId) {
                    console.warn('No authenticated user found in localStorage');
                    return;
                  }

                  console.log('Using authenticated user ID for account check:', userId);
                  const accountResponse = await fetch('/api/connect/my-account', {
                    headers: getAuthHeaders()
                  });
                  const accountData = await accountResponse.json();

                  if (accountData.success) {
                    console.log('✅ Login successful! Refreshing page...');

                    // Update the results to show successful login
                    setResults(prev => ({
                      ...prev,
                      [apiEndpoint.endpoint]: {
                        ...responseData,
                        loginCompleted: true,
                        accountConnected: true,
                        connectedAccount: accountData.account,
                        message: `✅ Successfully connected to Stripe account: ${accountData.account.email}`
                      }
                    }));

                    // Show loading state and refresh the page
                    setLoading(apiEndpoint.endpoint);
                    setTimeout(() => {
                      window.location.reload();
                    }, 2000);

                  } else {
                    // Login was not completed or failed
                    setResults(prev => ({
                      ...prev,
                      [apiEndpoint.endpoint]: {
                        ...responseData,
                        loginCompleted: false,
                        message: 'Login popup closed but no account connection detected. Please try again.'
                      }
                    }));
                  }
                } catch (error) {
                  console.error('Error checking login status:', error);
                }
              } else {
                // Check if popup URL has changed to our callback URL (indicates success)
                try {
                  const popupUrl = popup.location.href;
                  if (popupUrl && popupUrl.includes('/payment') && popupUrl.includes('connected=true')) {
                    console.log('✅ Login detected via URL change! Closing popup and refreshing...');
                    popup.close();
                    clearInterval(checkClosed);

                    // Show success message and refresh
                    setResults(prev => ({
                      ...prev,
                      [apiEndpoint.endpoint]: {
                        ...responseData,
                        loginCompleted: true,
                        message: '✅ Login successful! Refreshing page...'
                      }
                    }));

                    setLoading(apiEndpoint.endpoint);
                    setTimeout(() => {
                      window.location.reload();
                    }, 1000);
                  }
                } catch (error) {
                  // Cross-origin error is expected when popup is on Stripe domain
                  // This is normal and we'll continue monitoring
                }
              }
            }, 1000);

            responseData.modalOpened = true;
            responseData.message = 'Centered login window opened. Complete authentication in the popup.';
          } else {
            responseData.error = 'Popup blocked. Please allow popups for this site and try again.';
            responseData.success = false;
          }
        } else {
          // Open in popup (default behavior)
          const popup = window.open(
            responseData.loginUrl,
            'stripe-connect-login',
            responseData.popupFeatures || 'width=600,height=700,scrollbars=yes,resizable=yes,status=yes,location=yes'
          );

        if (popup) {
          // Monitor popup for completion and auto-close on success
          const checkClosed = setInterval(async () => {
            if (popup.closed) {
              clearInterval(checkClosed);
              console.log('Stripe Connect login popup closed - checking login status...');

              // Auto-check if login was successful by calling my-account endpoint
              try {
                const userId = getAuthenticatedUserId();
                if (!userId) {
                  console.warn('No authenticated user found in localStorage');
                  return;
                }

                console.log('Using authenticated user ID for account check:', userId);
                const accountResponse = await fetch('/api/connect/my-account', {
                  headers: getAuthHeaders()
                });
                const accountData = await accountResponse.json();

                if (accountData.success) {
                  console.log('✅ Login successful! Refreshing page...');

                  // Update the results to show successful login
                  setResults(prev => ({
                    ...prev,
                    [apiEndpoint.endpoint]: {
                      ...responseData,
                      loginCompleted: true,
                      accountConnected: true,
                      connectedAccount: accountData.account,
                      message: `✅ Successfully connected to Stripe account: ${accountData.account.email}`
                    }
                  }));

                  // Also update the my-account results
                  setResults(prev => ({
                    ...prev,
                    '/api/connect/my-account': accountData
                  }));

                  // Show loading state and refresh the page
                  setLoading(apiEndpoint.endpoint);
                  setTimeout(() => {
                    window.location.reload();
                  }, 2000);

                } else {
                  // Login was not completed or failed
                  setResults(prev => ({
                    ...prev,
                    [apiEndpoint.endpoint]: {
                      ...responseData,
                      loginCompleted: false,
                      message: 'Login popup closed but no account connection detected. Please try again.'
                    }
                  }));
                }
              } catch (error) {
                console.error('Error checking login status:', error);
                setResults(prev => ({
                  ...prev,
                  [apiEndpoint.endpoint]: {
                    ...responseData,
                    loginCompleted: false,
                    message: 'Error checking login status. Please try again.'
                  }
                }));
              }
            } else {
              // Check if popup URL has changed to our callback URL (indicates success)
              try {
                const popupUrl = popup.location.href;
                if (popupUrl && popupUrl.includes('/payment') && popupUrl.includes('connected=true')) {
                  console.log('✅ Login detected via URL change! Closing popup and refreshing...');
                  popup.close();
                  clearInterval(checkClosed);

                  // Show success message and refresh
                  setResults(prev => ({
                    ...prev,
                    [apiEndpoint.endpoint]: {
                      ...responseData,
                      loginCompleted: true,
                      message: '✅ Login successful! Refreshing page...'
                    }
                  }));

                  setLoading(apiEndpoint.endpoint);
                  setTimeout(() => {
                    window.location.reload();
                  }, 1000);
                }
              } catch (error) {
                // Cross-origin error is expected when popup is on Stripe domain
                // This is normal and we'll continue monitoring
              }
            }
          }, 1000);

          // Update response to indicate popup was opened
          responseData.popupOpened = true;
          responseData.message = 'Login popup opened. Complete authentication in the popup window.';
        } else {
            responseData.error = 'Popup blocked. Please allow popups for this site and try again.';
            responseData.success = false;
          }
        }
      }

      // Special handling for Embedded Checkout - redirect to checkout page with client secret
      if (apiEndpoint.endpoint === '/api/checkout' && responseData.success && responseData.clientSecret) {
        const params = new URLSearchParams();

        // Add the form data as URL parameters for the checkout page
        Object.entries(processedData).forEach(([key, value]) => {
          if (value && key !== 'clientSecret') {
            params.append(key, value.toString());
          }
        });

        // Store the client secret temporarily (you might want to use a more secure method)
        sessionStorage.setItem('stripe_client_secret', responseData.clientSecret);
        sessionStorage.setItem('stripe_session_id', responseData.sessionId);
        sessionStorage.setItem('stripe_transaction_id', responseData.transactionId);

        // Redirect to embedded checkout page
        const checkoutUrl = `/checkout?${params.toString()}`;
        responseData.message = `✅ Checkout session created! Redirecting to embedded checkout...`;
        responseData.redirecting = true;

        // Redirect after a short delay to show the success message
        setTimeout(() => {
          window.location.href = checkoutUrl;
        }, 1500);
      }

      // Auto-fetch charge ID if we got a transaction ID
      let enhancedResponseData = responseData;
      if (responseData.success && responseData.transactionId && !responseData.chargeId) {
        try {
          console.log('Auto-fetching charge ID for transaction:', responseData.transactionId);
          const transactionResponse = await fetch(`/api/transactions/${responseData.transactionId}`);
          if (transactionResponse.ok) {
            const transactionData = await transactionResponse.json();
            if (transactionData.success && transactionData.paymentIntent?.latest_charge) {
              enhancedResponseData = {
                ...responseData,
                autoFetchedChargeId: transactionData.paymentIntent.latest_charge,
                paymentIntentId: transactionData.paymentIntent.id,
                paymentIntentStatus: transactionData.paymentIntent.status
              };
              console.log('✅ Auto-fetched charge ID:', transactionData.paymentIntent.latest_charge);
            }
          }
        } catch (error) {
          console.log('Could not auto-fetch charge ID:', error);
        }
      }

      setResults(prev => ({ ...prev, [apiEndpoint.endpoint]: enhancedResponseData }));
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [apiEndpoint.endpoint]: {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }));
    } finally {
      setLoading(null);
    }
  };

  const apiEndpoints: Record<string, ApiEndpoint[]> = {
    checkout: [
      {
        name: '💳 Embedded Checkout Session',
        endpoint: '/api/checkout',
        method: 'POST',
        description: 'Create an embedded Stripe checkout session with user tracking',
        fields: [
          { name: 'userId', type: 'text', required: true, placeholder: 'user_123' },
          { name: 'userEmail', type: 'email', required: true, placeholder: '<EMAIL>' },
          { name: 'amount', type: 'number', required: false, placeholder: '1000' },
          { name: 'currency', type: 'text', required: false, placeholder: 'usd' },
          { name: 'productName', type: 'text', required: false, placeholder: 'Demo Product' },
          { name: 'productDescription', type: 'text', required: false, placeholder: 'Product description' }
        ]
      },
      {
        name: '🔒 Embedded Escrow Checkout',
        endpoint: '/api/checkout',
        method: 'POST',
        description: 'Create an embedded escrow payment with multi-stage releases (10/10/80%)',
        fields: [
          { name: 'userId', type: 'text', required: true, placeholder: 'buyer_user_123' },
          { name: 'userEmail', type: 'email', required: true, placeholder: '<EMAIL>' },
          { name: 'sellerId', type: 'text', required: true, placeholder: 'seller_user_456' },
          { name: 'sellerEmail', type: 'email', required: true, placeholder: '<EMAIL>' },
          { name: 'sellerStripeAccountId', type: 'text', required: true, placeholder: 'acct_...' },
          { name: 'orderId', type: 'text', required: true, placeholder: 'order_789' },
          { name: 'amount', type: 'number', required: true, placeholder: '10400' },
          { name: 'currency', type: 'text', required: false, placeholder: 'usd' },
          { name: 'productName', type: 'text', required: false, placeholder: 'Service Order' },
          { name: 'productDescription', type: 'text', required: false, placeholder: 'Service description' },
          { name: 'isEscrow', type: 'select', required: true, options: ['true'] }
        ]
      }
    ],
    escrow: [
      {
        name: '🚀 Create Escrow Payment with Checkout URL',
        endpoint: '/api/escrow/create',
        method: 'POST',
        description: 'STEP 1: Create escrow payment and get checkout URL for user to complete payment authorization',
        fields: [
          { name: 'userId', type: 'text', required: true, placeholder: 'buyer_user_123' },
          { name: 'userEmail', type: 'email', required: true, placeholder: '<EMAIL>' },
          { name: 'sellerId', type: 'text', required: true, placeholder: 'seller_user_456' },
          { name: 'sellerEmail', type: 'email', required: true, placeholder: '<EMAIL>' },
          { name: 'sellerStripeAccountId', type: 'text', required: true, placeholder: 'acct_...' },
          { name: 'orderId', type: 'text', required: true, placeholder: 'order_789' },
          { name: 'amount', type: 'number', required: true, placeholder: '10400' },
          { name: 'currency', type: 'text', required: false, placeholder: 'usd' },
          { name: 'productName', type: 'text', required: true, placeholder: 'Service Order' },
          { name: 'productDescription', type: 'text', required: false, placeholder: 'Service description' }
        ]
      },
      {
        name: 'Release Escrow Stage',
        endpoint: '/api/escrow/release',
        method: 'POST',
        description: 'Manually release an escrow stage (10%, 10%, or 80%). Automatically detects currency and uses correct Stripe instance.',
        fields: [
          { name: 'orderId', type: 'text', required: true, placeholder: 'order_21' },
          { name: 'stage', type: 'select', required: true, options: ['accept', 'delivered', 'completed'] },
          { name: 'chargeId', type: 'text', required: true, placeholder: 'ch_...' }
        ]
      },
      // {
      //   name: '🔍 Find Correct Charge ID',
      //   endpoint: '/api/escrow/release',
      //   method: 'GET',
      //   description: 'Find the correct charge ID for your escrow transaction (solves currency mismatch issues)',
      //   fields: [
      //     { name: 'orderId', type: 'text', required: true, placeholder: 'order_005' },
      //     { name: 'findChargeId', type: 'select', required: true, options: ['true'] }
      //   ]
      // },
      
      
    

      // {
      //   name: '🔍 Check Payment Intent Status',
      //   endpoint: '/api/stripe/payment-intent/status',
      //   method: 'GET',
      //   description: 'STEP 2a: Check if payment is authorized and ready for capture',
      //   fields: [
      //     { name: 'payment_intent_id', type: 'text', required: true, placeholder: 'pi_...' }
      //   ]
      // },
      {
        name: '🐛 Debug Escrow Transaction',
        endpoint: '/api/escrow/debug',
        method: 'GET',
        description: 'STEP 2b: Debug transaction, seller account, and payment status',
        fields: [
          { name: 'orderId', type: 'text', required: true, placeholder: 'order_789' }
        ]
      },
      {
        name: '🚫 Cancel Authorized Payment',
        endpoint: '/api/stripe/cancel',
        method: 'POST',
        description: 'Cancel an authorized payment intent (requires_capture, requires_confirmation, etc.) - Works with Payment Intent ID, Charge ID, or Transaction ID',
        fields: [
          { name: 'paymentIntentId', type: 'text', required: false, placeholder: 'pi_...' },
          { name: 'chargeId', type: 'text', required: false, placeholder: 'ch_...' },
          { name: 'transactionId', type: 'text', required: false, placeholder: 'Transaction ID (auto-resolves to Payment Intent)' },
          { name: 'cancellationReason', type: 'select', required: false, options: ['requested_by_customer', 'duplicate', 'fraudulent', 'abandoned'] },
          { name: 'currency', type: 'text', required: false, placeholder: 'usd' },
          { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
        ]
      },
      
    ],
    
    transaction: [
      {
        name: 'Get Transaction by ID',
        endpoint: '/api/transactions/{transactionId}',
        method: 'GET',
        description: 'Get transaction details including Payment Intent ID',
        fields: [
          { name: 'transactionId', type: 'text', required: true, placeholder: 'pYnAssf4p2DckHaoBTl0' }
        ]
      },
      
      {
        name: '🧾 Generate Invoice from Transaction',
        endpoint: '/api/transactions/{transactionId}/invoice',
        method: 'GET',
        description: 'Auto-generate complete invoice with payment intent, charge ID, and money breakdown',
        fields: [
          { name: 'transactionId', type: 'text', required: true, placeholder: 'EmNgsYGATgJ0pOo4JC25' }
        ]
      }
    ],
    // customer: [
    //   {
    //     name: 'Retrieve/Create Customer',
    //     endpoint: '/api/stripe/customer/retrieve',
    //     method: 'POST',
    //     description: 'Get existing customer or create new one by email/userId (Firebase UID)',
    //     fields: [
    //       { name: 'email', type: 'email', required: false, placeholder: '<EMAIL>' },
    //       { name: 'userId', type: 'text', required: false, placeholder: 'Firebase UID (will be used as Stripe customer ID)' },
    //       { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
    //     ]
    //   },
      
    // ],
    // account: [
 
    //   {
    //     name: 'Retrieve Account',
    //     endpoint: '/api/stripe/account/retrieve',
    //     method: 'POST',
    //     description: 'Get Stripe account details by ID',
    //     fields: [
    //       { name: 'accountId', type: 'text', required: true, placeholder: 'acct_...' },
    //       { name: 'isUS', type: 'select', required: false, options: ['false', 'true'] }
    //     ]
    //   },
    // ],
   

   

  
    connect: [
      {
        name: 'Connect Onboard',
        endpoint: '/api/connect/onboard',
        method: 'POST',
        description: 'Create Express account and onboarding link with user email',
        fields: [
          { name: 'email', type: 'text', required: false, placeholder: '<EMAIL>' }
        ]
      },
      {
        name: 'Connect Login (Popup)',
        endpoint: '/api/connect/login-oauth',
        method: 'POST',
        description: 'Login to existing Stripe account using OAuth popup - no account ID needed',
        fields: []
      },
      // {
      //   name: 'Connect Login (Centered)',
      //   endpoint: '/api/connect/login-oauth',
      //   method: 'POST',
      //   description: 'Login to existing Stripe account using centered popup window - no account ID needed',
      //   fields: [
      //     { name: 'useModal', type: 'select', required: true, options: ['true'] }
      //   ]
      // },
      {
        name: 'Get My Account Details',
        endpoint: '/api/connect/my-account',
        method: 'GET',
        description: 'Retrieve full account details for your connected Stripe account',
        fields: []
      },
      {
        name: 'Check Onboarding Status',
        endpoint: '/api/connect/onboarding-complete',
        method: 'GET',
        description: 'Check the onboarding completion status for a Stripe account',
        fields: [
          { name: 'accountId', type: 'text', required: true, placeholder: 'acct_1234567890' }
        ]
      },
      {
        name: 'Update Onboarding Status',
        endpoint: '/api/connect/onboarding-complete',
        method: 'POST',
        description: 'Manually update onboarding status after Stripe onboarding completion',
        fields: [
          { name: 'accountId', type: 'text', required: true, placeholder: 'acct_1234567890' }
        ]
      }
    ]
  };

  const renderField = (endpoint: string, field: ApiField) => {
    const value = formData[endpoint]?.[field.name] || '';

    if (field.type === 'select') {
      return (
        <select
          value={value}
          onChange={(e) => handleInputChange(endpoint, field.name, e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">Select...</option>
          {field.options?.map(option => (
            <option key={option} value={option}>{option}</option>
          ))}
        </select>
      );
    }

    if (field.type === 'textarea') {
      return (
        <textarea
          value={value}
          onChange={(e) => handleInputChange(endpoint, field.name, e.target.value)}
          placeholder={field.placeholder}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          rows={3}
        />
      );
    }

    return (
      <input
        type={field.type}
        value={value}
        onChange={(e) => handleInputChange(endpoint, field.name, e.target.value)}
        placeholder={field.placeholder}
        className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
    );
  };

  // Function to extract and display key information from API responses
  const renderKeyInformation = (result: ApiResponse) => {
    const keyInfo: Array<{label: string, value: string, type: 'success' | 'info' | 'warning'}> = [];

    // Extract Transaction ID
    if (result.transactionId) {
      keyInfo.push({label: 'Transaction ID', value: result.transactionId, type: 'success'});
    }

    // Extract Charge ID from various possible locations
    if (result.chargeId) {
      keyInfo.push({label: 'Charge ID', value: result.chargeId, type: 'success'});
    }
    if (result.primaryChargeId) {
      keyInfo.push({label: 'Primary Charge ID', value: result.primaryChargeId, type: 'success'});
    }
    if (result.autoFetchedChargeId) {
      keyInfo.push({label: '🔍 Auto-Fetched Charge ID', value: result.autoFetchedChargeId, type: 'success'});
    }
    if (result.summary?.chargeId) {
      keyInfo.push({label: 'Charge ID (Summary)', value: result.summary.chargeId, type: 'success'});
    }
    // Direct Stripe charge object
    if (result.id && result.object === 'charge') {
      keyInfo.push({label: '💳 Stripe Charge ID', value: result.id, type: 'success'});
    }
    // Charge from charges array
    if (result.charges?.data?.length > 0) {
      result.charges.data.forEach((charge: any, index: number) => {
        keyInfo.push({label: `Charge ID #${index + 1}`, value: charge.id, type: 'success'});
      });
    }
    // Latest charge from payment intent
    if (result.latest_charge) {
      keyInfo.push({label: 'Latest Charge ID', value: result.latest_charge, type: 'success'});
    }

    // Extract Session ID
    if (result.sessionId) {
      keyInfo.push({label: 'Session ID', value: result.sessionId, type: 'info'});
    }
    if (result.session?.id) {
      keyInfo.push({label: 'Stripe Session ID', value: result.session.id, type: 'info'});
    }

    // Extract Payment Intent ID
    if (result.paymentIntentId) {
      keyInfo.push({label: 'Payment Intent ID', value: result.paymentIntentId, type: 'info'});
    }
    if (result.paymentIntent?.id) {
      keyInfo.push({label: 'Payment Intent ID', value: result.paymentIntent.id, type: 'info'});
    }
    // Direct from Stripe charge object
    if (result.payment_intent && typeof result.payment_intent === 'string') {
      keyInfo.push({label: 'Payment Intent ID (from charge)', value: result.payment_intent, type: 'info'});
    }
    // Payment intent object
    if (result.id && result.object === 'payment_intent') {
      keyInfo.push({label: '💳 Payment Intent ID', value: result.id, type: 'info'});
    }

    // Extract Transfer ID
    if (result.transferId) {
      keyInfo.push({label: 'Transfer ID', value: result.transferId, type: 'success'});
    }

    // Extract Order ID
    if (result.orderId) {
      keyInfo.push({label: 'Order ID', value: result.orderId, type: 'info'});
    }

    // Extract Amount Information
    if (result.amount) {
      const amountDisplay = typeof result.amount === 'number'
        ? `$${(result.amount / 100).toFixed(2)}`
        : result.amount;
      keyInfo.push({label: 'Amount', value: amountDisplay, type: 'info'});
    }
    if (result.summary?.amountInMajorUnit) {
      keyInfo.push({label: 'Amount', value: `$${result.summary.amountInMajorUnit} ${result.summary.currency || ''}`, type: 'info'});
    }
    // Stripe charge amounts
    if (result.amount_captured && result.object === 'charge') {
      keyInfo.push({label: 'Amount Captured', value: `$${(result.amount_captured / 100).toFixed(2)} ${result.currency?.toUpperCase()}`, type: 'info'});
    }
    if (result.amount_capturable && result.object === 'payment_intent') {
      keyInfo.push({label: 'Amount Capturable', value: `$${(result.amount_capturable / 100).toFixed(2)} ${result.currency?.toUpperCase()}`, type: 'warning'});
    }

    // Extract Status Information
    if (result.status) {
      const statusType = ['succeeded', 'paid', 'completed'].includes(result.status) ? 'success' :
                        ['requires_capture', 'authorized', 'pending'].includes(result.status) ? 'warning' :
                        result.status === 'canceled' ? 'info' : 'info';
      keyInfo.push({label: 'Status', value: result.status, type: statusType});
    }

    // Payment Intent cancellation specific information
    if (result.paymentIntent?.status === 'canceled') {
      keyInfo.push({label: 'Payment Intent Status', value: 'canceled', type: 'info'});
      if (result.paymentIntent.cancellation_reason) {
        keyInfo.push({label: 'Cancellation Reason', value: result.paymentIntent.cancellation_reason, type: 'info'});
      }
      if (result.paymentIntent.canceled_at) {
        const canceledDate = new Date(result.paymentIntent.canceled_at * 1000).toLocaleString();
        keyInfo.push({label: 'Canceled At', value: canceledDate, type: 'info'});
      }
    }

    // Show how payment intent was resolved
    if (result.resolvedFrom) {
      if (result.resolvedFrom.transactionId) {
        keyInfo.push({label: 'Resolved From', value: `Transaction ID: ${result.resolvedFrom.transactionId}`, type: 'info'});
      } else if (result.resolvedFrom.chargeId) {
        keyInfo.push({label: 'Resolved From', value: `Charge ID: ${result.resolvedFrom.chargeId}`, type: 'info'});
      }
    }
    if (result.summary?.status) {
      keyInfo.push({label: 'Charge Status', value: result.summary.status, type: result.summary.status === 'succeeded' ? 'success' : 'warning'});
    }
    // Stripe charge specific statuses
    if (result.captured !== undefined && result.object === 'charge') {
      keyInfo.push({label: 'Captured', value: result.captured ? 'Yes' : 'No', type: result.captured ? 'success' : 'warning'});
    }
    if (result.paid !== undefined && result.object === 'charge') {
      keyInfo.push({label: 'Paid', value: result.paid ? 'Yes' : 'No', type: result.paid ? 'success' : 'warning'});
    }

    // Extract URL (for checkout)
    if (result.url) {
      keyInfo.push({label: 'Checkout URL', value: result.url, type: 'info'});
    }

    if (keyInfo.length === 0) return null;

    return (
      <div className="p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-md border-2 border-blue-200">
        <h4 className="text-lg font-bold text-gray-900 mb-3 flex items-center">
          <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Key Information
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {keyInfo.map((info, index) => (
            <div key={index} className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-600">{info.label}</label>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  info.type === 'success' ? 'bg-green-100 text-green-800' :
                  info.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {info.type === 'success' ? '✓' : info.type === 'warning' ? '⚠' : 'ℹ'}
                </span>
              </div>
              <div className="mt-1">
                {info.label.includes('URL') ? (
                  <a
                    href={info.value}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm font-mono text-blue-600 hover:text-blue-800 underline break-all"
                  >
                    {info.value}
                  </a>
                ) : (
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded break-all flex-1">
                      {info.value}
                    </p>
                    <button
                      onClick={(e) => {
                        navigator.clipboard.writeText(info.value);
                        // Simple feedback - you could enhance this with a toast
                        const btn = e.target as HTMLButtonElement;
                        const originalText = btn.textContent;
                        btn.textContent = '✓';
                        setTimeout(() => {
                          btn.textContent = originalText;
                        }, 1000);
                      }}
                      className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded transition-colors"
                      title="Copy to clipboard"
                    >
                      📋
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Quick copy section for commonly used IDs
  const renderQuickCopySection = (result: ApiResponse) => {
    const ids = [];

    // Collect all IDs
    if (result.transactionId) ids.push({label: 'Transaction ID', value: result.transactionId});
    if (result.chargeId) ids.push({label: 'Charge ID', value: result.chargeId});
    if (result.primaryChargeId) ids.push({label: 'Primary Charge ID', value: result.primaryChargeId});
    if (result.autoFetchedChargeId) ids.push({label: 'Auto-Fetched Charge ID', value: result.autoFetchedChargeId});
    if (result.id && result.object === 'charge') ids.push({label: 'Stripe Charge ID', value: result.id});
    if (result.latest_charge) ids.push({label: 'Latest Charge ID', value: result.latest_charge});
    if (result.orderId) ids.push({label: 'Order ID', value: result.orderId});
    if (result.paymentIntentId) ids.push({label: 'Payment Intent ID', value: result.paymentIntentId});
    if (result.id && result.object === 'payment_intent') ids.push({label: 'Payment Intent ID', value: result.id});
    if (result.payment_intent && typeof result.payment_intent === 'string') ids.push({label: 'Payment Intent ID', value: result.payment_intent});
    if (result.paymentIntent?.id) ids.push({label: 'Payment Intent ID (Canceled)', value: result.paymentIntent.id});
    if (result.sessionId) ids.push({label: 'Session ID', value: result.sessionId});
    if (result.session?.id) ids.push({label: 'Stripe Session ID', value: result.session.id});
    if (result.transferId) ids.push({label: 'Transfer ID', value: result.transferId});

    if (ids.length === 0) return null;

    return (
      <div className="p-4 bg-yellow-50 border-2 border-yellow-200 rounded-md">
        <h4 className="text-md font-bold text-gray-900 mb-3 flex items-center">
          <svg className="w-5 h-5 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          Quick Copy IDs
        </h4>
        <div className="space-y-2">
          {ids.map((id, index) => (
            <div key={index} className="flex items-center justify-between bg-white p-2 rounded border">
              <div className="flex-1">
                <span className="text-sm font-medium text-gray-700">{id.label}:</span>
                <span className="ml-2 text-sm font-mono text-gray-900">{id.value}</span>
              </div>
              <button
                onClick={(e) => {
                  navigator.clipboard.writeText(id.value);
                  // Simple feedback
                  const btn = e.target as HTMLButtonElement;
                  const originalText = btn.textContent;
                  btn.textContent = '✓ Copied!';
                  btn.className = btn.className.replace('bg-blue-500', 'bg-green-500');
                  setTimeout(() => {
                    btn.textContent = originalText;
                    btn.className = btn.className.replace('bg-green-500', 'bg-blue-500');
                  }, 1500);
                }}
                className="ml-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs transition-colors"
              >
                Copy
              </button>
            </div>
          ))}
        </div>
        <div className="mt-3 text-xs text-gray-600">
          💡 <strong>Tip:</strong> Use these IDs in other API calls. Transaction ID + Charge ID are commonly needed for escrow operations.
        </div>
      </div>
    );
  };

  const renderApiEndpoint = (apiEndpoint: ApiEndpoint, index: number) => {
    const result = results[apiEndpoint.endpoint];
    const isLoading = loading === apiEndpoint.endpoint;

    return (
      <div key={`${apiEndpoint.endpoint}-${apiEndpoint.name}-${index}`} className="bg-white rounded-lg shadow-md p-6 mb-4">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{apiEndpoint.name}</h3>
            <p className="text-sm text-gray-600">{apiEndpoint.description}</p>
            <div className="flex items-center mt-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {apiEndpoint.method}
              </span>
              <span className="ml-2 text-sm text-gray-500 font-mono">{apiEndpoint.endpoint}</span>
            </div>
          </div>
        </div>

        {apiEndpoint.fields.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            {apiEndpoint.fields.map(field => (
              <div key={field.name}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {field.name}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                </label>
                {renderField(apiEndpoint.endpoint, field)}
              </div>
            ))}
          </div>
        )}

        <div className="flex items-center justify-between">
          <button
            onClick={() => testApi(apiEndpoint)}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-md font-medium transition-colors"
          >
            {isLoading ? 'Testing...' : 'Test API'}
          </button>
        </div>

        {result && (
          <div className="mt-4 space-y-4">
            {/* Key Information Display */}
            {renderKeyInformation(result)}

            {/* Quick Copy Section for IDs */}
            {renderQuickCopySection(result)}

            {/* Full Response */}
            <div className="p-4 bg-gray-50 rounded-md">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Full Response:</h4>
              <pre className="text-xs text-gray-700 overflow-x-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    );
  };

  const QuickCheckout = () => {
    const [user, setUser] = useState<any>(null);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
      const checkAuth = async () => {
        try {
          const { auth } = await initFirebase();
          const currentUser = auth.currentUser;
          if (currentUser) {
            setUser(currentUser);
          } else {
            // Try to get user from localStorage
            const storedUser = localStorage.getItem('user');
            if (storedUser) {
              setUser(JSON.parse(storedUser));
            }
          }
        } catch (error) {
          console.error('Error checking auth:', error);
        }
      };

      checkAuth();
    }, []);

   

    return (
      <div className="space-y-4">
        {user ? (
          <div className="bg-blue-50 p-4 rounded-md">
            <p className="text-sm text-blue-800 mb-2">
              Logged in as: <span className="font-medium">{user.email}</span>
            </p>
            <p className="text-xs text-blue-600">
              User ID: {user.uid}
            </p>
          </div>
        ) : (
          <div className="bg-yellow-50 p-4 rounded-md">
            <p className="text-sm text-yellow-800">
              Please log in to make a payment with your account details.
            </p>
          </div>
        )}

        
      </div>
    );
  };

  // Authentication status component
  const AuthStatus = () => {
    const [user, setUser] = useState(getAuthenticatedUser());

    useEffect(() => {
      const checkAuth = () => {
        setUser(getAuthenticatedUser());
      };

      // Check auth on mount and when localStorage changes
      checkAuth();

      // Listen for storage events (login/logout in other tabs)
      const handleStorageChange = (e: StorageEvent) => {
        if (e.key === 'user') {
          checkAuth();
        }
      };

      window.addEventListener('storage', handleStorageChange);
      return () => window.removeEventListener('storage', handleStorageChange);
    }, []);

    return (
      <div className="mb-6 p-4 border rounded-lg bg-white shadow-sm">
        <h3 className="font-medium text-gray-900 mb-2">Authentication & Onboarding Status</h3>
        {user ? (
          <div className="space-y-2">
            <div className="text-green-600">
              <p className="flex items-center">
                <span className="mr-2">✅</span>
                Authenticated as: {user.email || user.uid}
              </p>
              <p className="text-xs text-gray-500 mt-1">User ID: {user.uid}</p>
            </div>

            {/* Onboarding Status */}
            {onboardingStatus && (
              <div className="mt-3 p-3 bg-blue-50 rounded-md">
                <h4 className="font-medium text-blue-900 mb-1">Stripe Onboarding Status</h4>
                <div className="text-sm space-y-1">
                  <p className={`flex items-center ${onboardingStatus.complete ? 'text-green-600' : 'text-yellow-600'}`}>
                    <span className="mr-2">{onboardingStatus.complete ? '✅' : '⏳'}</span>
                    Onboarding: {onboardingStatus.complete ? 'Complete' : 'Incomplete'}
                  </p>
                  <p className={`flex items-center ${onboardingStatus.chargesEnabled ? 'text-green-600' : 'text-red-600'}`}>
                    <span className="mr-2">{onboardingStatus.chargesEnabled ? '✅' : '❌'}</span>
                    Charges: {onboardingStatus.chargesEnabled ? 'Enabled' : 'Disabled'}
                  </p>
                  <p className={`flex items-center ${onboardingStatus.payoutsEnabled ? 'text-green-600' : 'text-red-600'}`}>
                    <span className="mr-2">{onboardingStatus.payoutsEnabled ? '✅' : '❌'}</span>
                    Payouts: {onboardingStatus.payoutsEnabled ? 'Enabled' : 'Disabled'}
                  </p>
                </div>
              </div>
            )}

            {/* Show onboarding completion result if available */}
            {results['onboarding-complete'] && (
              <div className="mt-3 p-3 bg-green-50 rounded-md">
                <p className="text-sm text-green-800">{results['onboarding-complete'].message}</p>
              </div>
            )}
          </div>
        ) : (
          <div className="text-red-600">
            <p className="flex items-center">
              <span className="mr-2">❌</span>
              Not authenticated
            </p>
            <p className="text-xs text-gray-500 mt-1">Please log in to use Stripe Connect features</p>
          </div>
        )}
      </div>
    );
  };

  const categories = Object.keys(apiEndpoints);

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Stripe API Tester</h1>
              <p className="text-gray-600">Test all your Stripe API endpoints with dynamic forms</p>
            </div>
            <div className="mt-4 sm:mt-0">
              {/* <button
                onClick={() => window.location.href = '/transactions'}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md font-medium"
              >
                View Transaction History
              </button> */}
            </div>
          </div>

          {/* Authentication Status */}
          <AuthStatus />

          {/* ID Helper Section - Coming Soon */}
        </div>

        {/* Category Navigation */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-2">
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={`px-4 py-2 rounded-md font-medium transition-colors ${
                  activeCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                }`}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
                <span className="ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                  {apiEndpoints[category].length}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* API Endpoints */}
        <div className="space-y-4">
          {apiEndpoints[activeCategory]?.map((endpoint, index) => renderApiEndpoint(endpoint, index))}
        </div>

        {/* Quick Checkout */}
        <div className="mt-12 p-6 bg-white rounded-lg shadow-md">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Checkout</h2>
          <div className="space-y-4">
            <QuickCheckout />
          </div>
        </div>


      </div>
    </div>
  );
}
