import type { NextApiRequest, NextApiResponse } from 'next';
import Strip<PERSON> from 'stripe';
import { db } from '@/lib/firebaseAdmin';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    console.log('checkout-to-seller endpoint called with method:', req.method);
    console.log('Request body:', req.body);

    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { sellerId } = req.body;
    if (!sellerId) return res.status(400).json({ error: 'Missing sellerId' });

    console.log('Looking up seller:', sellerId);

    // 1. Look up seller’s Stripe account ID in Firestore
    const doc = await db.collection('sellers').doc(sellerId).get();
    console.log('Firestore doc exists:', doc.exists);

    if (!doc.exists) return res.status(404).json({ error: 'Seller not found' });

    const data = doc.data();
    console.log('Seller data:', data);

    const { stripeAccountId } = data!;
    if (!stripeAccountId) return res.status(404).json({ error: 'Seller has no Stripe account' });

    console.log('Creating Stripe session for account:', stripeAccountId);

    // 2. Create a Stripe Checkout session ON THE SELLER'S CONNECTED ACCOUNT
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [{
        price_data: {
          currency: 'usd',
          product_data: { name: 'Test Product' },
          unit_amount: 1000, // $10
        },
        quantity: 1,
      }],
      mode: 'payment',
      success_url: `${req.headers.origin}/payment-success?sellerId=${sellerId}`,
      cancel_url: `${req.headers.origin}/payment-cancelled`,
      // Optional: collect buyer email, shipping, etc.
    }, {
      stripeAccount: stripeAccountId, // <-- THIS sends money directly to the seller!
    });

    console.log('Stripe session created successfully:', session.id);
    res.status(200).json({ url: session.url });
  } catch (error) {
    console.error('Error in checkout-to-seller:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ error: errorMessage });
  }
};
