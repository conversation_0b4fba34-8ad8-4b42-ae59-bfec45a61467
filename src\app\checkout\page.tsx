'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import EmbeddedCheckoutComponent from '@/components/EmbeddedCheckout';
import useAuth from '@/hook';

const CheckoutPage: React.FC = () => {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const router = useRouter();
  const auth = useAuth();

  // Get parameters from URL
  const amount = searchParams.get('amount') || '1000';
  const productName = searchParams.get('productName') || 'Demo Product';
  const productDescription = searchParams.get('productDescription') || '';
  const isEscrow = searchParams.get('isEscrow') === 'true';
  const sellerId = searchParams.get('sellerId');
  const sellerEmail = searchParams.get('sellerEmail');
  const sellerStripeAccountId = searchParams.get('sellerStripeAccountId');
  const orderId = searchParams.get('orderId');

  useEffect(() => {
    if (!auth.isLogin || !auth.userData) {
      router.push('/login');
      return;
    }

    // First, try to get client secret from sessionStorage (if redirected from payment page)
    const storedClientSecret = sessionStorage.getItem('stripe_client_secret');

    if (storedClientSecret) {
      setClientSecret(storedClientSecret);
      setLoading(false);
      return;
    }

    // If no stored client secret, create a new checkout session
    const createCheckoutSession = async () => {
      try {
        setLoading(true);
        setError(null);

        const requestBody = {
          userId: auth.userData.uid,
          userEmail: auth.userData.email,
          amount: parseInt(amount),
          productName,
          productDescription,
          isEscrow,
          ...(isEscrow && {
            sellerId,
            sellerEmail,
            sellerStripeAccountId,
            orderId
          })
        };

        const response = await fetch('/api/checkout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create checkout session');
        }

        const data = await response.json();
        setClientSecret(data.clientSecret);

        // Store for potential future use
        sessionStorage.setItem('stripe_client_secret', data.clientSecret);
        sessionStorage.setItem('stripe_session_id', data.sessionId);
        sessionStorage.setItem('stripe_transaction_id', data.transactionId);
      } catch (err) {
        console.error('Error creating checkout session:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    createCheckoutSession();
  }, [auth.userData, amount, productName, productDescription, isEscrow, sellerId, sellerEmail, sellerStripeAccountId, orderId, router]);

  const handleComplete = () => {
    // Get stored session info for redirect
    const sessionId = sessionStorage.getItem('stripe_session_id');
    const transactionId = sessionStorage.getItem('stripe_transaction_id');

    // Clean up sessionStorage
    sessionStorage.removeItem('stripe_client_secret');
    sessionStorage.removeItem('stripe_session_id');
    sessionStorage.removeItem('stripe_transaction_id');

    // Redirect to success page with session info
    const params = new URLSearchParams();
    if (sessionId) params.append('session_id', sessionId);
    if (transactionId) params.append('transaction_id', transactionId);
    if (isEscrow && orderId) params.append('order_id', orderId);

    router.push(`/payment-success?${params.toString()}`);
  };

  if (!auth.isLogin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
          <p>Please log in to continue with your purchase.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Setting up your checkout...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2 text-red-600">Error</h2>
          <p className="mb-4">{error}</p>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (!clientSecret) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Setup Error</h2>
          <p>Unable to initialize checkout. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h1 className="text-2xl font-bold mb-4">Complete Your Purchase</h1>
          <div className="border-b pb-4 mb-4">
            <h3 className="font-semibold">{productName}</h3>
            {productDescription && (
              <p className="text-gray-600 text-sm">{productDescription}</p>
            )}
            <p className="text-lg font-bold mt-2">
              ${(parseInt(amount) / 100).toFixed(2)}
            </p>
            {isEscrow && (
              <div className="mt-2 text-sm text-blue-600">
                <span className="bg-blue-100 px-2 py-1 rounded">Escrow Payment</span>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <EmbeddedCheckoutComponent
            clientSecret={clientSecret}
            onComplete={handleComplete}
          />
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
