import { useState } from 'react';

export default function BuyFromSeller() {
  const [loading, setLoading] = useState(false);

  const handlePaySeller = async () => {
    setLoading(true);
    // Hardcoded seller for demo — in real app, this is dynamic!
    const sellerId = "seller_123";
    const res = await fetch('/api/checkout-to-seller', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ sellerId }),
    });
    const data = await res.json();
    console.log('Redirecting to:', data.url);
    console.log(sellerId);// Go to Stripe Checkout
    window.location.href = data.url;

  };

  return (
    <div>
      <h1>Buy from Seller</h1>
      <button onClick={handlePaySeller} disabled={loading}>
        {loading ? "Redirecting..." : "Pay Seller $10"}
      </button>
    </div>
  );
}
